import '../entities/premium.dart';

/// Repository interface for Premium status data operations
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
abstract class PremiumRepository {
  /// Create or update premium status
  Future<void> setPremiumStatus(Premium premium);

  /// Get premium status by device ID
  Future<Premium?> getPremiumStatus(String userDeviceId);

  /// Check if user has premium access
  Future<bool> isPremiumUser(String userDeviceId);

  /// Update purchase verification timestamp
  Future<void> updateLastVerified(String userDeviceId, DateTime lastVerified);

  /// Remove premium status (for testing/debugging)
  Future<void> removePremiumStatus(String userDeviceId);

  /// Get purchase token for verification
  Future<String?> getPurchaseToken(String userDeviceId);
}
