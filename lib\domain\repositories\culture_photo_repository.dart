import '../entities/culture_photo.dart';

/// Repository interface for CulturePhoto data operations
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
abstract class CulturePhotoRepository {
  /// Create a new culture photo
  Future<void> createCulturePhoto(CulturePhoto photo);

  /// Get culture photo by ID
  Future<CulturePhoto?> getCulturePhotoById(String id);

  /// Get all photos for a culture
  Future<List<CulturePhoto>> getPhotosByCultureId(String cultureId);

  /// Get photos by stage
  Future<List<CulturePhoto>> getPhotosByStage(String cultureId, String stage);

  /// Update existing culture photo
  Future<void> updateCulturePhoto(CulturePhoto photo);

  /// Delete culture photo by ID
  Future<void> deleteCulturePhoto(String id);

  /// Delete all photos for a culture
  Future<void> deletePhotosByCultureId(String cultureId);

  /// Get photos count for a culture
  Future<int> getPhotosCount(String cultureId);

  /// Get total storage size for all photos
  Future<int> getTotalStorageSize();

  /// Get photos older than specified date for cleanup
  Future<List<CulturePhoto>> getPhotosOlderThan(DateTime date);
}
