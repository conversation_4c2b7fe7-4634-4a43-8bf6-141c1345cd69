# Testing Strategy

### Testing Pyramid

```
        E2E Tests (Widget Integration)
       /                            \
    Integration Tests (Repository & Services)
   /                                        \
Flutter Widget Tests              Dart Unit Tests
```

### Test Organization

**Flutter Widget Tests:**

```
test/widget_tests/
├── screens/
│   ├── recipe_management_screen_test.dart
│   └── culture_documentation_screen_test.dart
├── components/
│   ├── photo_capture_widget_test.dart
│   └── premium_gate_widget_test.dart
└── workflows/
    └── recipe_creation_flow_test.dart
```

**Integration Tests:**

```
integration_test/
├── offline_functionality_test.dart
├── premium_feature_integration_test.dart
└── analytics_calculation_test.dart
```

---

