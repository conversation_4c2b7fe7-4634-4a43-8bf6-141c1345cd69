# Technical Assumptions

### Repository Structure: Simplified Single App

**Decision:** Single Flutter application focused on individual personal use.

**Rationale:** Individual-use focus eliminates need for complex shared data models or team coordination features. Simple, focused codebase for personal productivity.

### Service Architecture: Pure Local Individual Application

**Decision:** 100% local Flutter mobile application for individual personal use with no external services, no internet connectivity requirements, and no multi-user features.

**Architecture Components:**

- **Individual User App:** Flutter mobile app for single-user personal use only
- **Personal Local Storage:** Basic SQLite database for individual user's personal data
- **Personal Photo Storage:** Device storage for individual user's culture photos
- **Individual Device Features:** Camera, notifications, file system for personal use only
- **No Team Features:** No sharing, no collaboration, no multi-user workflows

**Rationale:** Focuses entirely on individual productivity and personal data management. Eliminates all complexity related to team coordination, sharing, or institutional requirements.

### Testing Requirements: Individual User-Focused Testing

**Decision:** Unit testing for personal data operations, integration testing for individual workflows, manual testing for single-user experience.

**Testing Strategy:**

- **Unit Tests:** Personal data models, individual business logic, basic local storage
- **Integration Tests:** Personal workflows, individual device features
- **Manual Testing:** Single-user experience validation on personal devices
- **Individual Device Testing:** Personal usage patterns on consumer smartphones

**Rationale:** Testing focuses on individual user experience without team collaboration complexity.

### Additional Technical Assumptions and Requests

**Frontend Framework:**

- **Flutter 3.16+** for individual mobile application development
- **SQLite** for basic personal local data storage (no encryption complexity needed)
- **Google Drive API integration** for premium backup and restore functionality
- **Email and messaging app integration** for report sharing (share_plus package)
- **Simple file management** for personal photos and data
- **Basic device integration** for individual user features

**Development Tools:**

- **Git** for development version control
- **Flutter DevTools** for debugging and performance profiling
- **Simple testing frameworks** for individual user workflows

**Deployment Strategy:**

- **App Store/Google Play** distribution for individual consumers
- **Simple installation** with no setup requirements for personal use
- **Individual device activation** through app store purchase for premium features

**Performance Requirements:**

- **Personal offline operation** with no connectivity requirements
- **Individual photo management** with basic compression for personal device storage
- **Simple memory usage** optimized for personal device performance
- **Fast individual operations** optimized for single-user personal workflows

**Simplified Data Management:**

- **Personal local storage** using basic SQLite database for individual data
- **Google Drive backup** for premium users with automatic sync and restore capabilities
- **Report sharing functionality** enabling email export and messaging app integration (WhatsApp, Telegram, SMS)
- **Personal export functionality** for individual data portability (CSV, PDF) with sharing options
- **Individual data privacy** - all data stays private on personal device with optional premium cloud backup

