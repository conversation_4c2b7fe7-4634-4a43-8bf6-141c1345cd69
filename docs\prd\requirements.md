# Requirements

### Functional Requirements

**FR1:** The app shall provide digital recipe cards with essential parameters (ingredients, measurements, environmental conditions, timing) limited to 10 recipes in free tier, unlimited in premium

**FR2:** The app shall enable before/after photo comparison with basic timeline view, limited to 5 photos per culture in free tier, unlimited with advanced annotation tools in premium

**FR3:** The app shall provide mobile-friendly input forms for core culture tracking data including initiation date, transfer dates, growth observations, and success/failure outcomes

**FR4:** The app shall generate local notifications for sub-culture scheduling with customizable intervals, snooze options, and batch notification management for multiple cultures

**FR5:** The app shall calculate basic cost tracking for recipe ingredients with manual input in free tier, automatic cost calculations with supplier pricing in premium

**FR6:** The app shall operate fully offline with local SQLite data storage, ensuring field usability without internet connectivity

**FR7:** The app shall implement one-time $5 premium upgrade through simple app store purchase validation for individual use

**FR8:** The app shall provide personal data export functionality for recipes, culture records, and photo documentation in standard formats (CSV, PDF) with email sharing and external app integration (WhatsApp, messaging apps)

**FR9:** The app shall enable personal culture success rate tracking and analytics showing individual success percentages by recipe and time period

**FR10:** The app shall support photo annotation tools including text labels, measurement markers, and growth stage indicators for personal documentation (premium feature)

**FR11:** The app shall provide personal analytics dashboard showing individual cost per culture, success trends, and optimization recommendations with shareable report generation (premium feature)

**FR12:** The app shall enable personal device backup through Google Drive integration for premium users, allowing automatic backup and restore of user data across devices

**FR13:** The app shall provide personal storage management with photo compression and storage usage monitoring for individual device optimization

**FR14:** The app shall implement simple data migration tools for upgrading from free to premium tier on personal device

**FR15:** The app shall provide individual onboarding workflow with tissue culture best practices and app tutorials for personal use

**FR16:** The app shall provide basic data protection for personal recipe information stored on individual device

**FR17:** The app shall enable report sharing for small commercial operations through email export and integration with external messaging apps (WhatsApp, Telegram, SMS) for business communication

### Non-Functional Requirements

**NFR1:** The app shall launch within 3 seconds on target devices (iOS 12+, Android 8.0+) and maintain responsive performance during photo capture and data entry

**NFR2:** The app shall maintain reliable offline functionality for individual personal use

**NFR3:** The app shall use basic local data storage with standard platform security for personal device data protection

**NFR4:** The app shall maintain privacy through no-login architecture with no personal data collection or transmission

**NFR5:** The app shall support cross-platform compatibility on iOS and Android smartphones with responsive UI adapting to screen sizes 4.7" to 6.7"

**NFR6:** The app shall maintain personal data integrity through automatic local backups on individual device

**NFR7:** The app shall limit memory usage to under 200MB for optimal performance on personal devices

**NFR8:** The app shall provide basic accessibility features compliant with platform standards (VoiceOver, TalkBack) for individual users

**NFR9:** The app shall operate entirely offline with no network connectivity requirements for individual personal use

