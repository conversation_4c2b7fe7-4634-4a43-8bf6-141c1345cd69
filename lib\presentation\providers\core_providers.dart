import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/datasources/database_helper.dart';
import '../../data/repositories/culture_photo_repository_impl.dart';
import '../../data/repositories/culture_repository_impl.dart';
import '../../data/repositories/premium_repository_impl.dart';
import '../../data/repositories/recipe_repository_impl.dart';
import '../../domain/repositories/culture_photo_repository.dart';
import '../../domain/repositories/culture_repository.dart';
import '../../domain/repositories/premium_repository.dart';
import '../../domain/repositories/recipe_repository.dart';
import '../../shared/services/secure_storage_service.dart';

/// Core Riverpod providers for dependency injection
///
/// [Source: architecture/coding-standards.md - State Management: Use Riverpod providers for all state management]

// Database Helper Provider (Singleton)
final databaseHelperProvider = Provider<DatabaseHelper>((ref) {
  return DatabaseHelper();
});

// Secure Storage Service Provider (Singleton)
final secureStorageServiceProvider = Provider<SecureStorageService>((ref) {
  return SecureStorageService();
});

// Repository Providers - Following Repository Pattern
final recipeRepositoryProvider = Provider<RecipeRepository>((ref) {
  final databaseHelper = ref.read(databaseHelperProvider);
  return RecipeRepositoryImpl(databaseHelper);
});

final cultureRepositoryProvider = Provider<CultureRepository>((ref) {
  final databaseHelper = ref.read(databaseHelperProvider);
  return CultureRepositoryImpl(databaseHelper);
});

final culturePhotoRepositoryProvider = Provider<CulturePhotoRepository>((ref) {
  final databaseHelper = ref.read(databaseHelperProvider);
  return CulturePhotoRepositoryImpl(databaseHelper);
});

final premiumRepositoryProvider = Provider<PremiumRepository>((ref) {
  final databaseHelper = ref.read(databaseHelperProvider);
  return PremiumRepositoryImpl(databaseHelper);
});

// Application State Providers - Offline-First Design
final isOfflineModeProvider = StateProvider<bool>((ref) => true);

final databaseInitializationProvider = FutureProvider<void>((ref) async {
  final databaseHelper = ref.read(databaseHelperProvider);
  await databaseHelper.database; // Ensures database is initialized
});

// Premium Status Provider - Centralized Premium Feature Gating
final premiumStatusProvider = FutureProvider.family<bool, String>((ref, userDeviceId) async {
  final premiumRepository = ref.read(premiumRepositoryProvider);
  return await premiumRepository.isPremiumUser(userDeviceId);
});

// Device ID Provider - Platform-Native Security Integration
final deviceIdProvider = FutureProvider<String>((ref) async {
  final secureStorageService = ref.read(secureStorageServiceProvider);
  return await secureStorageService.getDeviceIdentifier();
});