import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';
import 'package:path/path.dart';
import 'package:culturestack/data/datasources/database_helper.dart';
import 'package:culturestack/data/repositories/recipe_repository_impl.dart';
import 'package:culturestack/domain/entities/recipe.dart';
import '../test_helpers.dart';

/// Integration test for Story 1.1: Local Data Foundation
///
/// Tests the complete offline-first data system including:
/// - Database initialization with mobile optimizations
/// - CRUD operations within performance requirements
/// - Error handling and recovery
/// - Platform security setup
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  setupPathProvider();
  group('Story 1.1 Integration Tests', () {
    late DatabaseHelper databaseHelper;
    late RecipeRepositoryImpl recipeRepository;
    const uuid = Uuid();

    setUpAll(() {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      databaseHelper = DatabaseHelper();
      recipeRepository = RecipeRepositoryImpl(databaseHelper);
      await databaseHelper.database; // Initialize database
    });

    tearDown(() async {
      // Clean up test database completely
      try {
        await databaseHelper.close();
        final dbPath = await getDatabasesPath();
        final dbFile = File(join(dbPath, 'culturestack.db'));
        if (await dbFile.exists()) {
          await dbFile.delete();
        }
      } catch (e) {
        // Ignore cleanup errors
      }
    });

    test('Complete workflow: Create, Read, Update, Delete recipe', () async {
      // Create a test recipe
      final recipe = Recipe(
        id: uuid.v4(),
        name: 'Integration Test Recipe',
        description: 'A recipe for testing the complete system',
        difficultyLevel: 'beginner',
        estimatedTime: 45,
        isPremium: false,
        successRate: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // CREATE: Add recipe to database
      await recipeRepository.createRecipe(recipe);

      // READ: Retrieve recipe
      final retrievedRecipe = await recipeRepository.getRecipeById(recipe.id);
      expect(retrievedRecipe, isNotNull);
      expect(retrievedRecipe!.name, recipe.name);
      expect(retrievedRecipe.description, recipe.description);

      // UPDATE: Modify recipe
      final updatedRecipe = recipe.copyWith(
        name: 'Updated Recipe Name',
        successRate: 85.5,
      );
      await recipeRepository.updateRecipe(updatedRecipe);

      // Verify update
      final retrievedUpdated = await recipeRepository.getRecipeById(recipe.id);
      expect(retrievedUpdated!.name, 'Updated Recipe Name');
      expect(retrievedUpdated.successRate, 85.5);

      // DELETE: Remove recipe
      await recipeRepository.deleteRecipe(recipe.id);

      // Verify deletion
      final deletedRecipe = await recipeRepository.getRecipeById(recipe.id);
      expect(deletedRecipe, isNull);
    });

    test('Performance requirement: CRUD operations under 500ms', () async {
      final recipe = Recipe(
        id: uuid.v4(),
        name: 'Performance Test Recipe',
        description: 'Testing performance requirements',
        difficultyLevel: 'intermediate',
        estimatedTime: 30,
        isPremium: false,
        successRate: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Test CREATE performance
      final createStopwatch = Stopwatch()..start();
      await recipeRepository.createRecipe(recipe);
      createStopwatch.stop();
      expect(createStopwatch.elapsedMilliseconds, lessThan(500));

      // Test READ performance
      final readStopwatch = Stopwatch()..start();
      await recipeRepository.getRecipeById(recipe.id);
      readStopwatch.stop();
      expect(readStopwatch.elapsedMilliseconds, lessThan(500));

      // Test UPDATE performance
      final updateStopwatch = Stopwatch()..start();
      await recipeRepository.updateRecipe(recipe.copyWith(name: 'Updated'));
      updateStopwatch.stop();
      expect(updateStopwatch.elapsedMilliseconds, lessThan(500));

      // Test DELETE performance
      final deleteStopwatch = Stopwatch()..start();
      await recipeRepository.deleteRecipe(recipe.id);
      deleteStopwatch.stop();
      expect(deleteStopwatch.elapsedMilliseconds, lessThan(500));
    });

    test('Database health and mobile optimizations', () async {
      // Verify database is healthy
      final isHealthy = await databaseHelper.checkDatabaseHealth();
      expect(isHealthy, isTrue);

      // Verify mobile optimizations are applied
      final db = await databaseHelper.database;

      // Check WAL mode
      final journalMode = await db.rawQuery('PRAGMA journal_mode');
      expect(journalMode.first['journal_mode'], 'wal');

      // Check foreign keys are enabled
      final foreignKeys = await db.rawQuery('PRAGMA foreign_keys');
      expect(foreignKeys.first['foreign_keys'], 1);

      // Check cache size
      final cacheSize = await db.rawQuery('PRAGMA cache_size');
      expect(cacheSize.first['cache_size'], 10000);
    });

    test('Offline-first functionality', () async {
      // This test verifies the system works without network connectivity
      // All operations should succeed since we're using local SQLite

      final recipe = Recipe(
        id: uuid.v4(),
        name: 'Offline Recipe',
        description: 'Testing offline-first architecture',
        difficultyLevel: 'advanced',
        estimatedTime: 60,
        isPremium: true,
        successRate: 92.5,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // All operations should work without network
      await recipeRepository.createRecipe(recipe);
      final retrieved = await recipeRepository.getRecipeById(recipe.id);
      expect(retrieved, isNotNull);

      // Search functionality
      final searchResults = await recipeRepository.searchRecipes('Offline');
      expect(searchResults.length, 1);
      expect(searchResults.first.name, 'Offline Recipe');

      // Count functionality - should be exactly 1 premium recipe from this test
      final count = await recipeRepository.getRecipesCount(isPremium: true);
      expect(count, equals(1));
    });

    test('Schema validation and data integrity', () async {
      final db = await databaseHelper.database;

      // Verify all required tables exist
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table'",
      );
      final tableNames = tables.map((t) => t['name']).toList();

      expect(tableNames, contains('recipes'));
      expect(tableNames, contains('cultures'));
      expect(tableNames, contains('culture_photos'));
      expect(tableNames, contains('premium_status'));

      // Verify indexes exist for performance
      final indexes = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='index'",
      );
      final indexNames = indexes.map((i) => i['name']).toList();

      expect(indexNames, contains('idx_recipes_created_at'));
      expect(indexNames, contains('idx_cultures_recipe_id'));
      expect(indexNames, contains('idx_culture_photos_culture_id'));

      // Verify triggers exist for data integrity
      final triggers = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='trigger'",
      );
      final triggerNames = triggers.map((t) => t['name']).toList();

      expect(triggerNames, contains('update_recipe_success_rate'));
      expect(triggerNames, contains('update_recipe_timestamp'));
    });
  });
}
