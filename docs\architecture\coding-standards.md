# Coding Standards

### Critical Flutter Rules

- **Repository Pattern:** Always access data through repository interfaces, never directly call SQLite operations from UI components
- **Premium Feature Gating:** All premium features must check status through the centralized `PremiumService`
- **Offline-First Design:** Never assume network connectivity - all features must work offline
- **State Management:** Use Riverpod providers for all state management
- **Photo Management:** Always compress images immediately during capture
- **Error Handling:** All async operations must include proper error handling
- **Database Transactions:** Use SQLite transactions for multi-step operations
- **Platform-Native Security:** Store sensitive data using platform-native security

### Naming Conventions

| Element | Convention | Example |
|---------|------------|----------|
| **Screens** | PascalCase + Screen suffix | `RecipeManagementScreen` |
| **Widgets** | PascalCase + Widget suffix | `PhotoCaptureWidget` |
| **Models** | PascalCase | `Recipe`, `CultureLog` |
| **Repositories** | PascalCase + Repository suffix | `RecipeRepository` |
| **Services** | PascalCase + Service suffix | `PremiumService` |
| **Providers** | camelCase + Provider suffix | `recipeProvider` |
| **Database Tables** | snake_case | `recipes`, `culture_logs` |

---

