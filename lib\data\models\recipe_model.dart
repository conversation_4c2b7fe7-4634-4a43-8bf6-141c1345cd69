import '../../domain/entities/recipe.dart';

/// Recipe model for data layer with SQLite serialization
///
/// [Source: architecture/database-schema.md]
class RecipeModel extends Recipe {
  const RecipeModel({
    required super.id,
    required super.name,
    required super.description,
    required super.difficultyLevel,
    required super.estimatedTime,
    required super.isPremium,
    required super.successRate,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create RecipeModel from Map (SQLite row)
  factory RecipeModel.fromMap(Map<String, dynamic> map) {
    return RecipeModel(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String? ?? '',
      difficultyLevel: map['difficulty_level'] as String,
      estimatedTime: map['estimated_time'] as int,
      isPremium: (map['is_premium'] as int) == 1,
      successRate: (map['success_rate'] as num).toDouble(),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// Convert RecipeModel to Map for SQLite storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'difficulty_level': difficultyLevel,
      'estimated_time': estimatedTime,
      'is_premium': isPremium ? 1 : 0,
      'success_rate': successRate,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create RecipeModel from Recipe entity
  factory RecipeModel.fromEntity(Recipe recipe) {
    return RecipeModel(
      id: recipe.id,
      name: recipe.name,
      description: recipe.description,
      difficultyLevel: recipe.difficultyLevel,
      estimatedTime: recipe.estimatedTime,
      isPremium: recipe.isPremium,
      successRate: recipe.successRate,
      createdAt: recipe.createdAt,
      updatedAt: recipe.updatedAt,
    );
  }

  /// Convert to Recipe entity
  Recipe toEntity() {
    return Recipe(
      id: id,
      name: name,
      description: description,
      difficultyLevel: difficultyLevel,
      estimatedTime: estimatedTime,
      isPremium: isPremium,
      successRate: successRate,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  @override
  RecipeModel copyWith({
    String? id,
    String? name,
    String? description,
    String? difficultyLevel,
    int? estimatedTime,
    bool? isPremium,
    double? successRate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecipeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      isPremium: isPremium ?? this.isPremium,
      successRate: successRate ?? this.successRate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
