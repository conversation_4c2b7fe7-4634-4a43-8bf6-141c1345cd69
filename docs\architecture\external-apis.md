# External APIs

### Google Play Billing API

- **Purpose:** Premium feature purchase processing and verification
- **Authentication:** App signing and Google Play Console configuration
- **Key Endpoints:** `BillingClient.queryPurchases()`, `BillingClient.launchBillingFlow()`
- **Integration Notes:** Offline verification support, 15% platform fee for revenue under $1M annually

### Google Drive API v3

- **Purpose:** Premium backup and restore functionality for user data
- **Base URL:** <https://www.googleapis.com/drive/v3>
- **Authentication:** OAuth 2.0 with limited scope permissions
- **Key Endpoints:** `POST /files`, `GET /files/{fileId}`, `GET /files`
- **Integration Notes:** Requires user consent, graceful offline degradation

---

