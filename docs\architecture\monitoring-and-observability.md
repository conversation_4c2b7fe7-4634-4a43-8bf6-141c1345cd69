# Monitoring and Observability

### Local Monitoring Stack

**Application Performance Metrics:**

- **Launch Time:** App startup duration (target: ≤ 3 seconds)
- **Database Query Performance:** SQLite operation timing (target: ≤ 100ms)
- **Photo Processing Time:** Image compression and storage duration (target: ≤ 2 seconds)
- **Memory Usage:** RAM consumption tracking (critical for 2GB+ devices)
- **Storage Usage:** Local database size and photo storage tracking

**System Health Metrics:**

- **Component Availability:** Health checks for each major component
- **External API Response Times:** Google Drive and Play Billing performance
- **Circuit Breaker Status:** External API failure tracking
- **Backup Queue Status:** Offline backup queue health (premium feature)

### Local Monitoring Implementation

```dart
class LocalMonitoringService {
  static Future<T> trackPerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();
      _recordPerformanceMetric(operationName, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      _logger.error('Operation failed: $operationName', e);
      rethrow;
    }
  }
  
  static Future<StorageHealth> checkStorageHealth() async {
    final dbSize = await _getDatabaseSize();
    final photoSize = await _getPhotoStorageSize();
    final totalStorage = await _getAvailableStorage();
    
    return StorageHealth(
      databaseSize: dbSize,
      photoStorageSize: photoSize,
      availableStorage: totalStorage,
      recommendCleanup: photoSize > totalStorage * 0.7,
    );
  }
}
```

---

