# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/UserInterfaceState.xcuserstate
**/ios/.generated/
**/ios/Flutter/Generated.xcconfig
**/ios/Runner/GeneratedPluginRegistrant.*

# macOS related
**/macos/Flutter/GeneratedPluginRegistrant.swift

# Coverage reports
coverage/
*.lcov

# Android related
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties
*.jks

# Web related
lib/generated_plugin_registrant.dart

# Windows related
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h

# Linux related
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugin_registrant.h

# Environment variables and secrets
.env
.env.local
.env.development
.env.production
*.secrets
secrets.json
google-services.json
GoogleService-Info.plist

# API keys and configuration
**/android/app/google-services.json
**/ios/Runner/GoogleService-Info.plist
**/lib/config/api_keys.dart
**/lib/config/secrets.dart

# Database files (local development)
*.db
*.sqlite
*.sqlite3

# Test related
test/coverage_helper_test.dart
integration_test/screenshots/

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output
**/fastlane/readme.md

# Firebase
.firebase/
**/android/app/google-services.json
**/ios/firebase_app_id_file.json

# Google Drive API credentials
credentials.json
token.json
client_secret.json

# Platform specific files
Thumbs.db
ehthumbs.db
Desktop.ini

# Node modules (if using any Node.js tools)
node_modules/
npm-debug.log
yarn-error.log

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# IDE and editor files
.vscode/settings.json
*.sublime-project
*.sublime-workspace

# Log files
logs/
*.log

# Build artifacts
*.apk
*.aab
*.ipa
*.dSYM.zip

# Generated files
**/generated/
**/*.g.dart
**/*.freezed.dart
**/*.mocks.dart

# Documentation build
/doc/

# Analysis and linting
.analyze_snapshot

# Flutter build cache
.flutter-cache/

# Project specific
# SQLite database files for local development
app.db
culture_stack.db
culturestack.db
culturestack_dev.db

# Photo storage directories (development)
photos/
images/
storage/
uploads/

# Backup files
*.backup
*.bak

# Cache directories
.cache/
cache/

# Performance profiling
*.profile

# Memory dumps
*.hprof

# Debug logs
debug.log
debug-*.log

# Coverage badges
coverage_badge.svg

# AI/ML model files (if any)
*.tflite
*.mlmodel

# Certificates and keystore files
*.p12
*.keystore
*.mobileprovision
*.certSigningRequest

# Deployment scripts output
deploy_output/
release_notes.txt