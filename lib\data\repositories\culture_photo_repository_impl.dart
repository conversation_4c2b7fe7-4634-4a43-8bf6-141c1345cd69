import 'dart:async';
import 'package:sqflite/sqflite.dart';

import '../../core/errors/exceptions.dart' as app_exceptions;
import '../../domain/entities/culture_photo.dart';
import '../../domain/repositories/culture_photo_repository.dart';
import '../datasources/database_helper.dart';
import '../models/culture_photo_model.dart';

/// CulturePhoto repository implementation with SQLite backend
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
class CulturePhotoRepositoryImpl implements CulturePhotoRepository {
  final DatabaseHelper _databaseHelper;

  // Performance requirement: CRUD operations under 500ms
  static const Duration _operationTimeout = Duration(milliseconds: 500);

  CulturePhotoRepositoryImpl(this._databaseHelper);

  @override
  Future<void> createCulturePhoto(CulturePhoto photo) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final photoModel = CulturePhotoModel.fromEntity(photo);

      await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
            await txn.insert(
              'culture_photos',
              photoModel.toMap(),
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }));
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to create culture photo: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<CulturePhoto?> getCulturePhotoById(String id) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'culture_photos',
          where: 'id = ?',
          whereArgs: [id],
          limit: 1,
        );
      });

      if (maps.isEmpty) return null;
      return CulturePhotoModel.fromMap(maps.first).toEntity();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get culture photo by ID: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<CulturePhoto>> getPhotosByCultureId(String cultureId) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'culture_photos',
          where: 'culture_id = ?',
          whereArgs: [cultureId],
          orderBy: 'capture_date ASC',
        );
      });

      return maps
          .map((map) => CulturePhotoModel.fromMap(map).toEntity())
          .toList();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get photos by culture ID: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<CulturePhoto>> getPhotosByStage(
      String cultureId, String stage) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'culture_photos',
          where: 'culture_id = ? AND stage = ?',
          whereArgs: [cultureId, stage],
          orderBy: 'capture_date ASC',
        );
      });

      return maps
          .map((map) => CulturePhotoModel.fromMap(map).toEntity())
          .toList();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get photos by stage: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> updateCulturePhoto(CulturePhoto photo) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final photoModel = CulturePhotoModel.fromEntity(photo);

      final rowsAffected = await _executeWithTimeout(
          () => _databaseHelper.transaction((txn) async {
                return await txn.update(
                  'culture_photos',
                  photoModel.toMap(),
                  where: 'id = ?',
                  whereArgs: [photo.id],
                );
              }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('CulturePhoto', photo.id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to update culture photo: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> deleteCulturePhoto(String id) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final rowsAffected = await _executeWithTimeout(
          () => _databaseHelper.transaction((txn) async {
                return await txn.delete(
                  'culture_photos',
                  where: 'id = ?',
                  whereArgs: [id],
                );
              }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('CulturePhoto', id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to delete culture photo: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> deletePhotosByCultureId(String cultureId) async {
    try {
      // AC3: Backup before critical operation (bulk delete)
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
            await txn.delete(
              'culture_photos',
              where: 'culture_id = ?',
              whereArgs: [cultureId],
            );
          }));
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to delete photos by culture ID: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<int> getPhotosCount(String cultureId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await _executeWithTimeout(() async {
        return await db.query(
          'culture_photos',
          columns: ['COUNT(*) as count'],
          where: 'culture_id = ?',
          whereArgs: [cultureId],
        );
      });

      return result.first['count'] as int;
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get photos count: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<int> getTotalStorageSize() async {
    try {
      final db = await _databaseHelper.database;

      final result = await _executeWithTimeout(() async {
        return await db.rawQuery('''
          SELECT SUM(LENGTH(file_path)) as total_size
          FROM culture_photos
        ''');
      });

      return (result.first['total_size'] as int?) ?? 0;
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get total storage size: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<CulturePhoto>> getPhotosOlderThan(DateTime date) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'culture_photos',
          where: 'capture_date < ?',
          whereArgs: [date.toIso8601String()],
          orderBy: 'capture_date ASC',
        );
      });

      return maps
          .map((map) => CulturePhotoModel.fromMap(map).toEntity())
          .toList();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get photos older than date: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Execute operation with timeout enforcement (500ms requirement)
  Future<T> _executeWithTimeout<T>(Future<T> Function() operation) async {
    try {
      return await operation().timeout(_operationTimeout);
    } on TimeoutException {
      throw app_exceptions.PerformanceException(
        'CulturePhoto repository operation exceeded timeout limit',
        timeout: _operationTimeout,
      );
    }
  }
}
