import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';

class Logger {
  static final Logger _instance = Logger._internal();
  factory Logger() => _instance;
  Logger._internal();

  static const String _logFileName = 'app_log.txt';
  File? _logFile;

  Future<void> _init() async {
    if (_logFile == null) {
      final directory = await getApplicationDocumentsDirectory();
      _logFile = File(join(directory.path, _logFileName));
    }
  }

  Future<void> log(String message) async {
    await _init();
    final timestamp = DateTime.now().toIso8601String();
    await _logFile!.writeAsString('$timestamp: $message\n', mode: FileMode.append);
  }
}
