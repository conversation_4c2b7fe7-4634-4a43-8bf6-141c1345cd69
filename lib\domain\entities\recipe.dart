/// Recipe entity representing tissue culture recipes
///
/// [Source: architecture/data-models.md#recipe-model]
class Recipe {
  final String id;
  final String name;
  final String description;
  final String difficultyLevel; // 'beginner', 'intermediate', 'advanced'
  final int estimatedTime; // minutes
  final bool isPremium;
  final double successRate; // percentage
  final DateTime createdAt;
  final DateTime updatedAt;

  const Recipe({
    required this.id,
    required this.name,
    required this.description,
    required this.difficultyLevel,
    required this.estimatedTime,
    required this.isPremium,
    required this.successRate,
    required this.createdAt,
    required this.updatedAt,
  });

  Recipe copyWith({
    String? id,
    String? name,
    String? description,
    String? difficultyLevel,
    int? estimatedTime,
    bool? isPremium,
    double? successRate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipe(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      isPremium: isPremium ?? this.isPremium,
      successRate: successRate ?? this.successRate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recipe && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Recipe(id: $id, name: $name, difficultyLevel: $difficultyLevel, isPremium: $isPremium)';
  }
}
