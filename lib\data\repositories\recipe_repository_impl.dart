import 'dart:async';
import 'package:sqflite/sqflite.dart';

import '../../core/errors/exceptions.dart' as app_exceptions;
import '../../domain/entities/recipe.dart';
import '../../domain/repositories/recipe_repository.dart';
import '../datasources/database_helper.dart';
import '../models/recipe_model.dart';

/// Recipe repository implementation with SQLite backend
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
class RecipeRepositoryImpl implements RecipeRepository {
  final DatabaseHelper _databaseHelper;

  // Performance requirement: CRUD operations under 500ms
  static const Duration _operationTimeout = Duration(milliseconds: 500);

  RecipeRepositoryImpl(this._databaseHelper);

  @override
  Future<void> createRecipe(Recipe recipe) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final recipeModel = RecipeModel.fromEntity(recipe);

      await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
        await txn.insert(
          'recipes',
          recipeModel.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }));
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to create recipe: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<Recipe?> getRecipeById(String id) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'recipes',
          where: 'id = ?',
          whereArgs: [id],
          limit: 1,
        );
      });

      if (maps.isEmpty) return null;
      return RecipeModel.fromMap(maps.first).toEntity();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get recipe by ID: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<Recipe>> getRecipes({
    String? difficultyLevel,
    bool? isPremium,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      if (difficultyLevel != null) {
        whereConditions.add('difficulty_level = ?');
        whereArgs.add(difficultyLevel);
      }

      if (isPremium != null) {
        whereConditions.add('is_premium = ?');
        whereArgs.add(isPremium ? 1 : 0);
      }

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'recipes',
          where:
              whereConditions.isNotEmpty ? whereConditions.join(' AND ') : null,
          whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
          orderBy: 'created_at DESC',
          limit: limit,
          offset: offset,
        );
      });

      return maps.map((map) => RecipeModel.fromMap(map).toEntity()).toList();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get recipes: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> updateRecipe(Recipe recipe) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final recipeModel = RecipeModel.fromEntity(recipe.copyWith(
        updatedAt: DateTime.now(),
      ));

      final rowsAffected = await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
        return await txn.update(
          'recipes',
          recipeModel.toMap(),
          where: 'id = ?',
          whereArgs: [recipe.id],
        );
      }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('Recipe', recipe.id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to update recipe: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> deleteRecipe(String id) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final rowsAffected = await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
        // Foreign key constraints will cascade delete cultures and photos
        return await txn.delete(
          'recipes',
          where: 'id = ?',
          whereArgs: [id],
        );
      }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('Recipe', id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to delete recipe: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<Recipe>> searchRecipes(String query) async {
    try {
      final db = await _databaseHelper.database;
      final searchPattern = '%${query.toLowerCase()}%';

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'recipes',
          where: 'LOWER(name) LIKE ? OR LOWER(description) LIKE ?',
          whereArgs: [searchPattern, searchPattern],
          orderBy: 'created_at DESC',
        );
      });

      return maps.map((map) => RecipeModel.fromMap(map).toEntity()).toList();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to search recipes: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<int> getRecipesCount({bool? isPremium}) async {
    try {
      final db = await _databaseHelper.database;

      String? where;
      List<dynamic>? whereArgs;

      if (isPremium != null) {
        where = 'is_premium = ?';
        whereArgs = [isPremium ? 1 : 0];
      }

      final result = await _executeWithTimeout(() async {
        return await db.query(
          'recipes',
          columns: ['COUNT(*) as count'],
          where: where,
          whereArgs: whereArgs,
        );
      });

      return result.first['count'] as int;
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get recipes count: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<Recipe?> getRecipeWithSuccessRate(String id) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.rawQuery('''
          SELECT r.*, 
            COALESCE(
              ROUND(
                (COUNT(CASE WHEN c.outcome = 'success' THEN 1 END) * 100.0) / 
                NULLIF(COUNT(CASE WHEN c.outcome IN ('success', 'failure', 'partial_success') THEN 1 END), 0), 
                2
              ), 
              0.0
            ) as calculated_success_rate
          FROM recipes r
          LEFT JOIN cultures c ON r.id = c.recipe_id
          WHERE r.id = ?
          GROUP BY r.id
        ''', [id]);
      });

      if (maps.isEmpty) return null;

      final map = Map<String, dynamic>.from(maps.first);
      final calculatedSuccessRate = map['calculated_success_rate'] as double;
      map['success_rate'] = calculatedSuccessRate;

      return RecipeModel.fromMap(map).toEntity();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get recipe with success rate: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Execute operation with timeout enforcement (500ms requirement)
  Future<T> _executeWithTimeout<T>(Future<T> Function() operation) async {
    try {
      return await operation().timeout(_operationTimeout);
    } on TimeoutException {
      throw app_exceptions.PerformanceException(
        'Recipe repository operation exceeded timeout limit',
        timeout: _operationTimeout,
      );
    }
  }
}
