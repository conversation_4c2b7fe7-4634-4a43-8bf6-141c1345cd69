/// CulturePhoto entity representing photos taken during culture process
///
/// [Source: architecture/database-schema.md]
class CulturePhoto {
  final String id;
  final String cultureId;
  final String filePath;
  final DateTime captureDate;
  final String stage; // 'initial', 'progress', 'final'
  final int compressionQuality; // Default 85 for mobile optimization
  final String? notes;

  const CulturePhoto({
    required this.id,
    required this.cultureId,
    required this.filePath,
    required this.captureDate,
    required this.stage,
    this.compressionQuality = 85,
    this.notes,
  });

  CulturePhoto copyWith({
    String? id,
    String? cultureId,
    String? filePath,
    DateTime? captureDate,
    String? stage,
    int? compressionQuality,
    String? notes,
  }) {
    return CulturePhoto(
      id: id ?? this.id,
      cultureId: cultureId ?? this.cultureId,
      filePath: filePath ?? this.filePath,
      captureDate: captureDate ?? this.captureDate,
      stage: stage ?? this.stage,
      compressionQuality: compressionQuality ?? this.compressionQuality,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CulturePhoto && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CulturePhoto(id: $id, cultureId: $cultureId, stage: $stage)';
  }
}
