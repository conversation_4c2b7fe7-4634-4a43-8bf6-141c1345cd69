# Frontend Architecture

### Clean Architecture Organization

```
lib/
├── core/                           # Core utilities and constants
│   ├── constants/
│   ├── errors/
│   ├── network/
│   └── utils/
├── data/                           # Data layer implementation
│   ├── datasources/
│   ├── models/
│   └── repositories/
├── domain/                         # Business logic layer
│   ├── entities/
│   ├── repositories/
│   └── usecases/
├── presentation/                   # UI layer
│   ├── screens/
│   ├── widgets/
│   └── providers/
└── shared/                         # Shared utilities
    ├── themes/
    ├── routing/
    └── services/
```

### State Management with Riverpod

```dart
// Core providers for app-wide state
final databaseProvider = Provider<DatabaseHelper>((ref) => DatabaseHelper());
final premiumServiceProvider = Provider<PremiumService>((ref) => PremiumService());

// Repository providers
final recipeRepositoryProvider = Provider<RecipeRepository>((ref) {
  return RecipeRepositoryImpl(ref.watch(databaseProvider));
});

// State providers for UI
final recipesProvider = StateNotifierProvider<RecipeNotifier, RecipeState>((ref) {
  return RecipeNotifier(ref.watch(recipeRepositoryProvider));
});
```

---

