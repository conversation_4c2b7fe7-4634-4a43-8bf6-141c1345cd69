import 'dart:async';
import 'package:sqflite/sqflite.dart';

import '../../core/errors/exceptions.dart' as app_exceptions;
import '../../domain/entities/culture.dart';
import '../../domain/repositories/culture_repository.dart';
import '../datasources/database_helper.dart';
import '../models/culture_model.dart';

/// Culture repository implementation with SQLite backend
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
class CultureRepositoryImpl implements CultureRepository {
  final DatabaseHelper _databaseHelper;

  // Performance requirement: CRUD operations under 500ms
  static const Duration _operationTimeout = Duration(milliseconds: 500);

  CultureRepositoryImpl(this._databaseHelper);

  @override
  Future<void> createCulture(Culture culture) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final cultureModel = CultureModel.fromEntity(culture);

      await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
            await txn.insert(
              'cultures',
              cultureModel.toMap(),
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }));
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to create culture: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<Culture?> getCultureById(String id) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'cultures',
          where: 'id = ?',
          whereArgs: [id],
          limit: 1,
        );
      });

      if (maps.isEmpty) return null;
      return CultureModel.fromMap(maps.first).toEntity();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get culture by ID: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<Culture>> getCultures({
    String? recipeId,
    String? outcome,
    DateTime? startDateFrom,
    DateTime? startDateTo,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      if (recipeId != null) {
        whereConditions.add('recipe_id = ?');
        whereArgs.add(recipeId);
      }

      if (outcome != null) {
        whereConditions.add('outcome = ?');
        whereArgs.add(outcome);
      }

      if (startDateFrom != null) {
        whereConditions.add('start_date >= ?');
        whereArgs.add(startDateFrom.toIso8601String());
      }

      if (startDateTo != null) {
        whereConditions.add('start_date <= ?');
        whereArgs.add(startDateTo.toIso8601String());
      }

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'cultures',
          where:
              whereConditions.isNotEmpty ? whereConditions.join(' AND ') : null,
          whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
          orderBy: 'created_at DESC',
          limit: limit,
          offset: offset,
        );
      });

      return maps.map((map) => CultureModel.fromMap(map).toEntity()).toList();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get cultures: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> updateCulture(Culture culture) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final cultureModel = CultureModel.fromEntity(culture);

      final rowsAffected = await _executeWithTimeout(
          () => _databaseHelper.transaction((txn) async {
                return await txn.update(
                  'cultures',
                  cultureModel.toMap(),
                  where: 'id = ?',
                  whereArgs: [culture.id],
                );
              }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('Culture', culture.id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to update culture: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> deleteCulture(String id) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final rowsAffected = await _executeWithTimeout(
          () => _databaseHelper.transaction((txn) async {
                // Foreign key constraints will cascade delete photos
                return await txn.delete(
                  'cultures',
                  where: 'id = ?',
                  whereArgs: [id],
                );
              }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('Culture', id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to delete culture: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<Culture>> getCulturesByRecipeId(String recipeId) async {
    return getCultures(recipeId: recipeId);
  }

  @override
  Future<int> getCulturesCount({String? recipeId, String? outcome}) async {
    try {
      final db = await _databaseHelper.database;

      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      if (recipeId != null) {
        whereConditions.add('recipe_id = ?');
        whereArgs.add(recipeId);
      }

      if (outcome != null) {
        whereConditions.add('outcome = ?');
        whereArgs.add(outcome);
      }

      final result = await _executeWithTimeout(() async {
        return await db.query(
          'cultures',
          columns: ['COUNT(*) as count'],
          where:
              whereConditions.isNotEmpty ? whereConditions.join(' AND ') : null,
          whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        );
      });

      return result.first['count'] as int;
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get cultures count: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<double> getSuccessRateForRecipe(String recipeId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await _executeWithTimeout(() async {
        return await db.rawQuery('''
          SELECT COALESCE(
            ROUND(
              (COUNT(CASE WHEN outcome = 'success' THEN 1 END) * 100.0) / 
              NULLIF(COUNT(CASE WHEN outcome IN ('success', 'failure', 'partial_success') THEN 1 END), 0), 
              2
            ), 
            0.0
          ) as success_rate
          FROM cultures 
          WHERE recipe_id = ?
        ''', [recipeId]);
      });

      return result.first['success_rate'] as double;
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get success rate for recipe: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<List<Culture>> getInProgressCultures() async {
    return getCultures(outcome: 'in_progress');
  }

  @override
  Future<void> updateCultureOutcome(String id, String outcome) async {
    try {
      final rowsAffected = await _executeWithTimeout(
          () => _databaseHelper.transaction((txn) async {
                return await txn.update(
                  'cultures',
                  {'outcome': outcome},
                  where: 'id = ?',
                  whereArgs: [id],
                );
              }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException('Culture', id);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to update culture outcome: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Execute operation with timeout enforcement (500ms requirement)
  Future<T> _executeWithTimeout<T>(Future<T> Function() operation) async {
    try {
      return await operation().timeout(_operationTimeout);
    } on TimeoutException {
      throw app_exceptions.PerformanceException(
        'Culture repository operation exceeded timeout limit',
        timeout: _operationTimeout,
      );
    }
  }
}
