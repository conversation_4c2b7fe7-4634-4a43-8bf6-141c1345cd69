import 'dart:io';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/errors/exceptions.dart';

/// Platform-native secure storage service
///
/// [Source: architecture/tech-stack.md - Local Security]
/// Uses iOS Keychain and Android EncryptedSharedPreferences
class SecureStorageService {
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  /// Store sensitive data securely
  /// Used for premium purchase tokens, device IDs, etc.
  Future<void> storeSecurely(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      throw SecurityException(
        'Failed to store data securely: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Retrieve sensitive data securely
  Future<String?> retrieveSecurely(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      throw SecurityException(
        'Failed to retrieve data securely: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Delete sensitive data
  Future<void> deleteSecurely(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      throw SecurityException(
        'Failed to delete data securely: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Clear all secure data (for logout/reset)
  Future<void> clearAllSecureData() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      throw SecurityException(
        'Failed to clear secure data: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Check if secure storage is available on this device
  Future<bool> isSecureStorageAvailable() async {
    try {
      // Test by writing and reading a value
      const testKey = '_secure_storage_test';
      const testValue = 'test';

      await _secureStorage.write(key: testKey, value: testValue);
      final retrievedValue = await _secureStorage.read(key: testKey);
      await _secureStorage.delete(key: testKey);

      return retrievedValue == testValue;
    } catch (e) {
      return false;
    }
  }

  /// Get unique device identifier for premium status
  /// Generates and stores a persistent device ID
  Future<String> getDeviceIdentifier() async {
    const deviceIdKey = 'device_identifier';

    try {
      // Try to get existing device ID
      String? deviceId = await retrieveSecurely(deviceIdKey);

      if (deviceId == null || deviceId.isEmpty) {
        // Generate new device ID
        deviceId = _generateDeviceId();
        await storeSecurely(deviceIdKey, deviceId);
      }

      return deviceId;
    } catch (e) {
      throw SecurityException(
        'Failed to get device identifier: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Store premium purchase token securely
  Future<void> storePurchaseToken(String token, String platform) async {
    const tokenKey = 'premium_purchase_token';
    const platformKey = 'premium_purchase_platform';

    try {
      await storeSecurely(tokenKey, token);
      await storeSecurely(platformKey, platform);
    } catch (e) {
      throw SecurityException(
        'Failed to store purchase token: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Retrieve premium purchase token
  Future<Map<String, String?>> getPurchaseToken() async {
    const tokenKey = 'premium_purchase_token';
    const platformKey = 'premium_purchase_platform';

    try {
      final token = await retrieveSecurely(tokenKey);
      final platform = await retrieveSecurely(platformKey);

      return {
        'token': token,
        'platform': platform,
      };
    } catch (e) {
      throw SecurityException(
        'Failed to retrieve purchase token: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Generate unique device identifier
  String _generateDeviceId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecondsSinceEpoch % 1000000;
    final platform = Platform.isIOS ? 'ios' : 'android';

    return '${platform}_${timestamp}_$random';
  }

  /// Store non-sensitive preferences
  /// For settings that don't require encryption
  Future<void> storePreference(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(key, value);
    } catch (e) {
      throw SecurityException(
        'Failed to store preference: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Retrieve non-sensitive preferences
  Future<String?> getPreference(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(key);
    } catch (e) {
      throw SecurityException(
        'Failed to get preference: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Validate platform security setup
  Future<bool> validateSecuritySetup() async {
    try {
      // Check if secure storage is available
      final isAvailable = await isSecureStorageAvailable();
      if (!isAvailable) return false;

      // Check if device identifier can be generated
      final deviceId = await getDeviceIdentifier();
      if (deviceId.isEmpty) return false;

      return true;
    } catch (e) {
      return false;
    }
  }
}
