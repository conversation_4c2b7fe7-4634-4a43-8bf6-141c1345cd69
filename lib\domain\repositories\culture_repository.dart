import '../entities/culture.dart';

/// Repository interface for Culture data operations
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
abstract class CultureRepository {
  /// Create a new culture
  Future<void> createCulture(Culture culture);

  /// Get culture by ID
  Future<Culture?> getCultureById(String id);

  /// Get all cultures with optional filtering
  Future<List<Culture>> getCultures({
    String? recipeId,
    String? outcome,
    DateTime? startDateFrom,
    DateTime? startDateTo,
    int? limit,
    int? offset,
  });

  /// Update existing culture
  Future<void> updateCulture(Culture culture);

  /// Delete culture by ID
  Future<void> deleteCulture(String id);

  /// Get cultures by recipe ID
  Future<List<Culture>> getCulturesByRecipeId(String recipeId);

  /// Get cultures count
  Future<int> getCulturesCount({String? recipeId, String? outcome});

  /// Get success rate for a specific recipe
  Future<double> getSuccessRateForRecipe(String recipeId);

  /// Get in-progress cultures
  Future<List<Culture>> getInProgressCultures();

  /// Update culture outcome
  Future<void> updateCultureOutcome(String id, String outcome);
}
