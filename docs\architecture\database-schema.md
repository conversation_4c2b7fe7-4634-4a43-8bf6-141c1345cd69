# Database Schema

### Mobile-Optimized SQLite Schema

```sql
-- Enable mobile performance optimizations
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA foreign_keys = ON;

-- Core Tables
CREATE TABLE recipes (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_time INTEGER,
    is_premium BOOLEAN DEFAULT FALSE,
    success_rate REAL DEFAULT 0.0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cultures (
    id TEXT PRIMARY KEY,
    recipe_id TEXT NOT NULL,
    batch_name TEXT NOT NULL,
    start_date DATETIME NOT NULL,
    outcome TEXT CHECK (outcome IN ('success', 'failure', 'partial_success', 'in_progress')),
    temperature REAL,
    humidity REAL,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE
);

CREATE TABLE culture_photos (
    id TEXT PRIMARY KEY,
    culture_id TEXT NOT NULL,
    file_path TEXT NOT NULL,
    capture_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    stage TEXT CHECK (stage IN ('initial', 'progress', 'final')),
    compression_quality INTEGER DEFAULT 85,
    notes TEXT,
    FOREIGN KEY (culture_id) REFERENCES cultures(id) ON DELETE CASCADE
);

CREATE TABLE premium_status (
    user_device_id TEXT PRIMARY KEY,
    is_premium BOOLEAN DEFAULT FALSE,
    purchase_token TEXT,
    purchase_date DATETIME,
    purchase_platform TEXT CHECK (purchase_platform IN ('google_play', 'apple_app_store')),
    last_verified DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Performance Indexes
CREATE INDEX idx_recipes_created_at ON recipes(created_at DESC);
CREATE INDEX idx_cultures_recipe_id ON cultures(recipe_id);
CREATE INDEX idx_culture_photos_culture_id ON culture_photos(culture_id);

-- Data Integrity Triggers
CREATE TRIGGER update_recipe_success_rate
AFTER INSERT ON cultures
BEGIN
    UPDATE recipes 
    SET success_rate = (
        SELECT ROUND(
            (COUNT(CASE WHEN outcome = 'success' THEN 1 END) * 100.0) / COUNT(*), 2
        )
        FROM cultures 
        WHERE recipe_id = NEW.recipe_id 
        AND outcome IN ('success', 'failure', 'partial_success')
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.recipe_id;
END;
```

---

