# Next Steps

### UX Expert Prompt

Analyze the CultureStack PRD and create comprehensive user experience architecture for the individual-focused tissue culture management app. Focus on:

**Priority 1: Individual User Journey Mapping**

- Design user flows for "Individual Hobbyist" vs "Small Commercial" onboarding paths
- Create wireframes for Epic 1 core screens: Personal Dashboard, Recipe Collection, Culture Timeline
- Optimize premium upgrade flow and feature discovery patterns

**Priority 2: Privacy-First UX Patterns**

- Design no-login onboarding that builds immediate trust and value
- Create intuitive offline-first interaction patterns
- Develop progressive disclosure for premium features without overwhelming free users

**Priority 3: Photo-Centric Design System**

- Design culture documentation workflows optimized for mobile photography
- Create visual identification systems for tissue culture tracking
- Develop photo annotation and labeling interfaces for scientific accuracy

**Deliverables:** User journey maps, wireframes for all Epic 1-2 screens, interaction prototypes, accessibility compliance validation.

### Architect Prompt

Design the technical architecture for CultureStack based on the comprehensive PRD specifications. Focus on:

**Priority 1: Offline-First Flutter Architecture**

- Design SQLite schema supporting all epic requirements with performance optimization
- Create local data management patterns for recipes, cultures, photos with transaction safety
- Architect photo storage and compression system targeting <200KB per image

**Priority 2: Premium Feature Integration**

- Design Google Drive API integration with secure backup/restore functionality
- Architect app store purchase validation and premium feature gating
- Create professional report generation system with PDF export capabilities

**Priority 3: Scalable Foundation for Future Analytics**

- Design data collection patterns supporting Future Epic 4 advanced analytics
- Create performance monitoring and optimization framework
- Architect modular system supporting post-MVP learning platform features

**Deliverables:** System architecture diagrams, database schema, API integration specifications, performance optimization strategy, development roadmap.
