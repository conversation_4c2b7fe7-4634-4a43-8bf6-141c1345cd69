import '../entities/recipe.dart';

/// Repository interface for Recipe data operations
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
abstract class RecipeRepository {
  /// Create a new recipe
  Future<void> createRecipe(Recipe recipe);

  /// Get recipe by ID
  Future<Recipe?> getRecipeById(String id);

  /// Get all recipes with optional filtering
  Future<List<Recipe>> getRecipes({
    String? difficultyLevel,
    bool? isPremium,
    int? limit,
    int? offset,
  });

  /// Update existing recipe
  Future<void> updateRecipe(Recipe recipe);

  /// Delete recipe by ID
  Future<void> deleteRecipe(String id);

  /// Search recipes by name or description
  Future<List<Recipe>> searchRecipes(String query);

  /// Get recipes count
  Future<int> getRecipesCount({bool? isPremium});

  /// Get recipe with success rate calculation
  Future<Recipe?> getRecipeWithSuccessRate(String id);
}
