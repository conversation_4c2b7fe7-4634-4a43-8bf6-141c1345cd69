import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:sqflite/sqflite.dart';
import 'package:culturestack/data/datasources/database_helper.dart';
import 'package:culturestack/core/errors/exceptions.dart' as app_exceptions;
import '../../test_helpers.dart';

@GenerateMocks([Database, Transaction])
import 'database_helper_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  setupPathProvider();
  group('DatabaseHelper', () {
    late DatabaseHelper databaseHelper;
    late MockDatabase mockDatabase;

    setUp(() {
      databaseHelper = DatabaseHelper();
      mockDatabase = MockDatabase();
      databaseHelper.setDatabase(mockDatabase);
    });

    group('transaction', () {
      test('should complete transaction successfully', () async {
        // Arrange
        when(mockDatabase.transaction(any)).thenAnswer((realInvocation) async {
          final action = realInvocation.positionalArguments[0] as Future<dynamic> Function(
              Transaction txn);
          final mockTransaction = MockTransaction();
          return await action(mockTransaction);
        });

        // Act
        final result = await databaseHelper.transaction((txn) async {
          return 'Success';
        });

        // Assert
        expect(result, 'Success');
        verify(mockDatabase.transaction(any));
      });

      test('should throw DatabaseException on transaction failure', () async {
        // Arrange
        when(mockDatabase.transaction(any)).thenThrow(Exception('Test Error'));

        // Act & Assert
        expect(
          () => databaseHelper.transaction((txn) async {}),
          throwsA(isA<app_exceptions.DatabaseException>()),
        );
        verify(mockDatabase.transaction(any));
      });

      test('should retry on database lock error and eventually succeed',
          () async {
        // Arrange
        int callCount = 0;
        when(mockDatabase.transaction(any)).thenAnswer((_) async {
          callCount++;
          if (callCount < 3) {
            throw Exception('database is locked');
          } else {
            return 'Success';
          }
        });

        // Act
        final result = await databaseHelper.transaction((txn) async {
          return 'Success';
        });

        // Assert
        expect(result, 'Success');
        expect(callCount, 3);
      });

      test('should throw DatabaseException after max retries on lock error',
          () async {
        // Arrange
        when(mockDatabase.transaction(any))
            .thenThrow(Exception('database is locked'));

        // Act & Assert
        expect(
          () => databaseHelper.transaction((txn) async {}),
          throwsA(isA<app_exceptions.DatabaseException>()),
        );
      });
    });

    group('checkDatabaseHealth', () {
      test('should return true when integrity check is ok', () async {
        // Arrange
        when(mockDatabase.rawQuery('PRAGMA integrity_check'))
            .thenAnswer((_) async => [
                  {'integrity_check': 'ok'}
                ]);

        // Act
        final result = await databaseHelper.checkDatabaseHealth();

        // Assert
        expect(result, isTrue);
      });

      test('should return false when integrity check fails', () async {
        // Arrange
        when(mockDatabase.rawQuery('PRAGMA integrity_check'))
            .thenAnswer((_) async => [
                  {'integrity_check': 'corruption detected'}
                ]);

        // Act
        final result = await databaseHelper.checkDatabaseHealth();

        // Assert
        expect(result, isFalse);
      });

      test('should throw DatabaseCorruptionException on query failure', () async {
        // Arrange
        when(mockDatabase.rawQuery('PRAGMA integrity_check'))
            .thenThrow(Exception('SQL Error'));

        // Act & Assert
        expect(
          () => databaseHelper.checkDatabaseHealth(),
          throwsA(isA<app_exceptions.DatabaseCorruptionException>()),
        );
      });
    });

    group('backup and restore', () {
      test('backupDatabase should succeed', () async {
        // Note: This would require mocking file system operations in a real test
        // For now, testing the method exists and follows expected interface
        expect(databaseHelper.backupDatabase, isA<Function>());
      });

      test('restoreDatabase should succeed', () async {
        // Note: This would require mocking file system operations in a real test
        // For now, testing the method exists and follows expected interface
        expect(databaseHelper.restoreDatabase, isA<Function>());
      });
    });
  });
}
