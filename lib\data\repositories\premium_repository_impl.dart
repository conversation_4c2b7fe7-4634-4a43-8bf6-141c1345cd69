import 'dart:async';
import 'package:sqflite/sqflite.dart';

import '../../core/errors/exceptions.dart' as app_exceptions;
import '../../domain/entities/premium.dart';
import '../../domain/repositories/premium_repository.dart';
import '../datasources/database_helper.dart';
import '../models/premium_model.dart';

/// Premium repository implementation with SQLite backend
///
/// [Source: architecture/coding-standards.md - Repository Pattern]
class PremiumRepositoryImpl implements PremiumRepository {
  final DatabaseHelper _databaseHelper;

  // Performance requirement: CRUD operations under 500ms
  static const Duration _operationTimeout = Duration(milliseconds: 500);

  PremiumRepositoryImpl(this._databaseHelper);

  @override
  Future<void> setPremiumStatus(Premium premium) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final premiumModel = PremiumModel.fromEntity(premium);

      await _executeWithTimeout(() => _databaseHelper.transaction((txn) async {
            await txn.insert(
              'premium_status',
              premiumModel.toMap(),
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }));
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to set premium status: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<Premium?> getPremiumStatus(String userDeviceId) async {
    try {
      final db = await _databaseHelper.database;

      final maps = await _executeWithTimeout(() async {
        return await db.query(
          'premium_status',
          where: 'user_device_id = ?',
          whereArgs: [userDeviceId],
          limit: 1,
        );
      });

      if (maps.isEmpty) return null;
      return PremiumModel.fromMap(maps.first).toEntity();
    } catch (e) {
      if (e is app_exceptions.PerformanceException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get premium status: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<bool> isPremiumUser(String userDeviceId) async {
    try {
      final premium = await getPremiumStatus(userDeviceId);
      return premium?.isPremium ?? false;
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.DatabaseException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to check premium user status: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> updateLastVerified(
      String userDeviceId, DateTime lastVerified) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final rowsAffected = await _executeWithTimeout(
          () => _databaseHelper.transaction((txn) async {
                return await txn.update(
                  'premium_status',
                  {'last_verified': lastVerified.toIso8601String()},
                  where: 'user_device_id = ?',
                  whereArgs: [userDeviceId],
                );
              }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException(
            'PremiumStatus', userDeviceId);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to update last verified: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<void> removePremiumStatus(String userDeviceId) async {
    try {
      // AC3: Backup before critical operation
      await _executeWithTimeout(() => _databaseHelper.backupDatabase());

      final rowsAffected = await _executeWithTimeout(
          () => _databaseHelper.transaction((txn) async {
                return await txn.delete(
                  'premium_status',
                  where: 'user_device_id = ?',
                  whereArgs: [userDeviceId],
                );
              }));

      if (rowsAffected == 0) {
        throw app_exceptions.EntityNotFoundException(
            'PremiumStatus', userDeviceId);
      }
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.EntityNotFoundException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to remove premium status: ${e.toString()}',
        originalError: e,
      );
    }
  }

  @override
  Future<String?> getPurchaseToken(String userDeviceId) async {
    try {
      final premium = await getPremiumStatus(userDeviceId);
      return premium?.purchaseToken;
    } catch (e) {
      if (e is app_exceptions.PerformanceException ||
          e is app_exceptions.DatabaseException) {
        rethrow;
      }
      throw app_exceptions.DatabaseException(
        'Failed to get purchase token: ${e.toString()}',
        originalError: e,
      );
    }
  }

  /// Execute operation with timeout enforcement (500ms requirement)
  Future<T> _executeWithTimeout<T>(Future<T> Function() operation) async {
    try {
      return await operation().timeout(_operationTimeout);
    } on TimeoutException {
      throw app_exceptions.PerformanceException(
        'Premium repository operation exceeded timeout limit',
        timeout: _operationTimeout,
      );
    }
  }
}
