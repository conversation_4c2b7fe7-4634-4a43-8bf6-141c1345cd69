# Error Handling Strategy

### Error Response Format

```dart
class AppError {
  final String code;
  final String message;
  final String userMessage;
  final ErrorSeverity severity;
  final DateTime timestamp;
  final bool canRetry;
  
  const AppError({
    required this.code,
    required this.message,
    required this.userMessage,
    required this.severity,
    required this.timestamp,
    this.canRetry = false,
  });
}

enum ErrorSeverity { info, warning, error, critical }
```

### Error Handling Implementation

```dart
class ErrorHandler {
  static AppError handleRepositoryError(Exception exception) {
    switch (exception.runtimeType) {
      case DatabaseException:
        return AppError(
          code: 'DB_ERROR',
          message: exception.toString(),
          userMessage: 'Unable to save data locally. Please try again.',
          severity: ErrorSeverity.error,
          timestamp: DateTime.now(),
          canRetry: true,
        );
      
      case NetworkException:
        return AppError(
          code: 'NETWORK_ERROR',
          message: exception.toString(),
          userMessage: 'Working offline. Some features may be limited.',
          severity: ErrorSeverity.warning,
          timestamp: DateTime.now(),
          canRetry: true,
        );
      
      default:
        return AppError(
          code: 'UNKNOWN_ERROR',
          message: exception.toString(),
          userMessage: 'Something went wrong. Please try again.',
          severity: ErrorSeverity.error,
          timestamp: DateTime.now(),
          canRetry: true,
        );
    }
  }
}
```

---

