/// Premium entity representing premium feature access
///
/// [Source: architecture/data-models.md#premium-model]
class Premium {
  final String userDeviceId;
  final bool isPremium;
  final String? purchaseToken;
  final DateTime? purchaseDate;
  final String? purchasePlatform; // 'google_play', 'apple_app_store'
  final DateTime lastVerified;

  const Premium({
    required this.userDeviceId,
    required this.isPremium,
    this.purchaseToken,
    this.purchaseDate,
    this.purchasePlatform,
    required this.lastVerified,
  });

  Premium copyWith({
    String? userDeviceId,
    bool? isPremium,
    String? purchaseToken,
    DateTime? purchaseDate,
    String? purchasePlatform,
    DateTime? lastVerified,
  }) {
    return Premium(
      userDeviceId: userDeviceId ?? this.userDeviceId,
      isPremium: isPremium ?? this.isPremium,
      purchaseToken: purchaseToken ?? this.purchaseToken,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      purchasePlatform: purchasePlatform ?? this.purchasePlatform,
      lastVerified: lastVerified ?? this.lastVerified,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Premium && other.userDeviceId == userDeviceId;
  }

  @override
  int get hashCode => userDeviceId.hashCode;

  @override
  String toString() {
    return 'Premium(userDeviceId: $userDeviceId, isPremium: $isPremium, platform: $purchasePlatform)';
  }
}
