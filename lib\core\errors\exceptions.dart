/// Core exceptions for the application
///
/// [Source: architecture/error-handling-strategy.md]
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppException(this.message, {this.code, this.originalError});

  @override
  String toString() =>
      'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Database-specific exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message, {super.code, super.originalError});

  @override
  String toString() =>
      'DatabaseException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Database initialization failure
class DatabaseInitializationException extends DatabaseException {
  const DatabaseInitializationException(super.message,
      {super.code, super.originalError});
}

/// Database corruption detected
class DatabaseCorruptionException extends DatabaseException {
  const DatabaseCorruptionException(super.message,
      {super.code, super.originalError});
}

/// Performance timeout exception
class PerformanceException extends AppException {
  final Duration timeout;

  const PerformanceException(super.message,
      {required this.timeout, super.code, super.originalError});

  @override
  String toString() =>
      'PerformanceException: $message (Timeout: ${timeout.inMilliseconds}ms)';
}

/// Platform security exception
class SecurityException extends AppException {
  const SecurityException(super.message, {super.code, super.originalError});
}

/// Entity not found exception
class EntityNotFoundException extends AppException {
  final String entityType;
  final String entityId;

  const EntityNotFoundException(this.entityType, this.entityId)
      : super('$entityType with ID $entityId not found');
}

/// Validation exception
class ValidationException extends AppException {
  final Map<String, String> fieldErrors;

  const ValidationException(super.message, {this.fieldErrors = const {}});
}
