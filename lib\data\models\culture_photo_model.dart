import '../../domain/entities/culture_photo.dart';

/// CulturePhoto model for data layer with SQLite serialization
///
/// [Source: architecture/database-schema.md]
class CulturePhotoModel extends CulturePhoto {
  const CulturePhotoModel({
    required super.id,
    required super.cultureId,
    required super.filePath,
    required super.captureDate,
    required super.stage,
    super.compressionQuality = 85,
    super.notes,
  });

  /// Create CulturePhotoModel from Map (SQLite row)
  factory CulturePhotoModel.fromMap(Map<String, dynamic> map) {
    return CulturePhotoModel(
      id: map['id'] as String,
      cultureId: map['culture_id'] as String,
      filePath: map['file_path'] as String,
      captureDate: DateTime.parse(map['capture_date'] as String),
      stage: map['stage'] as String,
      compressionQuality: map['compression_quality'] as int? ?? 85,
      notes: map['notes'] as String?,
    );
  }

  /// Convert CulturePhotoModel to Map for SQLite storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'culture_id': cultureId,
      'file_path': filePath,
      'capture_date': captureDate.toIso8601String(),
      'stage': stage,
      'compression_quality': compressionQuality,
      'notes': notes,
    };
  }

  /// Create CulturePhotoModel from CulturePhoto entity
  factory CulturePhotoModel.fromEntity(CulturePhoto photo) {
    return CulturePhotoModel(
      id: photo.id,
      cultureId: photo.cultureId,
      filePath: photo.filePath,
      captureDate: photo.captureDate,
      stage: photo.stage,
      compressionQuality: photo.compressionQuality,
      notes: photo.notes,
    );
  }

  /// Convert to CulturePhoto entity
  CulturePhoto toEntity() {
    return CulturePhoto(
      id: id,
      cultureId: cultureId,
      filePath: filePath,
      captureDate: captureDate,
      stage: stage,
      compressionQuality: compressionQuality,
      notes: notes,
    );
  }

  @override
  CulturePhotoModel copyWith({
    String? id,
    String? cultureId,
    String? filePath,
    DateTime? captureDate,
    String? stage,
    int? compressionQuality,
    String? notes,
  }) {
    return CulturePhotoModel(
      id: id ?? this.id,
      cultureId: cultureId ?? this.cultureId,
      filePath: filePath ?? this.filePath,
      captureDate: captureDate ?? this.captureDate,
      stage: stage ?? this.stage,
      compressionQuality: compressionQuality ?? this.compressionQuality,
      notes: notes ?? this.notes,
    );
  }
}
