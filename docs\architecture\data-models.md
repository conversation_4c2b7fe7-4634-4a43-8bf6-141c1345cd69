# Data Models

### Recipe Model

**Purpose:** Represents tissue culture recipes with ingredients, procedures, and success tracking

```typescript
interface Recipe {
  id: string;
  name: string;
  description: string;
  ingredients: Ingredient[];
  procedures: ProcedureStep[];
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number; // minutes
  successRate: number; // percentage
  createdAt: Date;
  updatedAt: Date;
  isPremium: boolean;
}
```

### Culture Model

**Purpose:** Documents individual culture attempts with photos, progress tracking, and outcomes

```typescript
interface Culture {
  id: string;
  recipeId: string;
  batchName: string;
  startDate: Date;
  photos: CulturePhoto[];
  notes: CultureNote[];
  environmentConditions: EnvironmentData;
  outcome: 'success' | 'failure' | 'partial_success' | 'in_progress';
  successIndicators: string[];
  createdAt: Date;
}
```

### Premium Model

**Purpose:** Manages premium feature access and purchase verification

```typescript
interface Premium {
  userDeviceId: string;
  premiumStatus: boolean;
  purchaseToken: string;
  purchaseDate: Date;
  expiryDate?: Date;
  featuresUnlocked: string[];
  purchasePlatform: 'google_play' | 'apple_app_store';
}
```

---

