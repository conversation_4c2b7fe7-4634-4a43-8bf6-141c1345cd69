/// Culture entity representing individual culture attempts
///
/// [Source: architecture/data-models.md#culture-model]
class Culture {
  final String id;
  final String recipeId;
  final String batchName;
  final DateTime startDate;
  final String
      outcome; // 'success', 'failure', 'partial_success', 'in_progress'
  final double? temperature;
  final double? humidity;
  final String? notes;
  final DateTime createdAt;

  const Culture({
    required this.id,
    required this.recipeId,
    required this.batchName,
    required this.startDate,
    required this.outcome,
    this.temperature,
    this.humidity,
    this.notes,
    required this.createdAt,
  });

  Culture copyWith({
    String? id,
    String? recipeId,
    String? batchName,
    DateTime? startDate,
    String? outcome,
    double? temperature,
    double? humidity,
    String? notes,
    DateTime? createdAt,
  }) {
    return Culture(
      id: id ?? this.id,
      recipeId: recipeId ?? this.recipeId,
      batchName: batchName ?? this.batchName,
      startDate: startDate ?? this.startDate,
      outcome: outcome ?? this.outcome,
      temperature: temperature ?? this.temperature,
      humidity: humidity ?? this.humidity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Check if culture is still in progress
  bool get isInProgress => outcome == 'in_progress';

  /// Check if culture was successful
  bool get isSuccessful => outcome == 'success';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Culture && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Culture(id: $id, recipeId: $recipeId, batchName: $batchName, outcome: $outcome)';
  }
}
