# CultureStack - Fullstack Architecture Document

## Table of Contents

1. [Tech Stack](#tech-stack)
2. [Data Models](#data-models)
3. [Components](#components)
4. [External APIs](#external-apis)
5. [Core Workflows](#core-workflows)
6. [Database Schema](#database-schema)

---

## Tech Stack

### Core Technologies

| Component | Technology | Version | Purpose | Why Chosen |
|-----------|------------|---------|---------|------------|
| **Mobile Framework** | Flutter | 3.16+ | Cross-platform mobile development | Mature ecosystem, offline-first capabilities, single codebase for iOS/Android |
| **Programming Language** | Dart | 3.2+ | Application development language | Native Flutter language, strong typing, excellent performance |
| **Local Database** | SQLite | 3.40+ | Offline-first data storage | Embedded database, reliable offline operation, excellent mobile performance |
| **State Management** | Riverpod | 2.4+ | Reactive state management | Compile-time safety, excellent testing support, provider pattern |
| **Payment Processing** | Google Play Billing | Latest | In-app purchase management | Platform compliance, 15% fee structure, offline verification support |
| **Cloud Storage (Premium)** | Google Drive API | v3 | Premium backup functionality | User-familiar platform, generous free storage, robust API |
| **Local Security** | Platform Native | - | Secure data storage | iOS Keychain, Android EncryptedSharedPreferences |
| **Image Processing** | Flutter Image | Latest | Photo compression and management | Built-in optimization, memory-efficient processing |
| **Testing Framework** | Flutter Test | Latest | Widget and unit testing | Comprehensive testing ecosystem, built-in mocking |
| **CI/CD** | GitHub Actions | Latest | Automated testing and deployment | Free for open source, excellent Flutter support |

### Architecture Patterns

- **Offline-First Mobile Architecture:** Complete functionality without internet connectivity
- **Clean Architecture:** Organized into core, data, domain, presentation, and shared layers
- **Repository Pattern:** Abstract data access layer with premium-aware methods
- **Provider Pattern:** Riverpod for dependency injection and state management
- **Circuit Breaker Pattern:** Graceful degradation for external API failures
- **Privacy-by-Design:** No-login architecture with local-only data storage

---

## Data Models

### Recipe Model

**Purpose:** Represents tissue culture recipes with ingredients, procedures, and success tracking

```typescript
interface Recipe {
  id: string;
  name: string;
  description: string;
  ingredients: Ingredient[];
  procedures: ProcedureStep[];
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number; // minutes
  successRate: number; // percentage
  createdAt: Date;
  updatedAt: Date;
  isPremium: boolean;
}
```

### Culture Model

**Purpose:** Documents individual culture attempts with photos, progress tracking, and outcomes

```typescript
interface Culture {
  id: string;
  recipeId: string;
  batchName: string;
  startDate: Date;
  photos: CulturePhoto[];
  notes: CultureNote[];
  environmentConditions: EnvironmentData;
  outcome: 'success' | 'failure' | 'partial_success' | 'in_progress';
  successIndicators: string[];
  createdAt: Date;
}
```

### Premium Model

**Purpose:** Manages premium feature access and purchase verification

```typescript
interface Premium {
  userDeviceId: string;
  premiumStatus: boolean;
  purchaseToken: string;
  purchaseDate: Date;
  expiryDate?: Date;
  featuresUnlocked: string[];
  purchasePlatform: 'google_play' | 'apple_app_store';
}
```

---

## Components

### 1. Recipe Management Component

**Responsibility:** Handles recipe creation, editing, storage, and retrieval with premium feature integration

**Key Interfaces:**

- `RecipeRepository` - Data persistence and retrieval
- `PremiumService` - Premium feature validation
- `ValidationService` - Recipe data validation

**Dependencies:** SQLite database, Premium Service

### 2. Culture Documentation Component  

**Responsibility:** Manages culture tracking, photo capture, progress documentation, and outcome recording

**Key Interfaces:**

- `CultureRepository` - Culture data management
- `PhotoService` - Image capture and compression
- `NotificationService` - Progress reminders

**Dependencies:** Camera service, File storage, Recipe Management Component

### 3. Premium Service Component

**Responsibility:** Centralized premium feature gating, purchase verification, and feature unlock management

**Key Interfaces:**

- `PurchaseValidator` - App store receipt verification
- `FeatureGatekeeper` - Premium feature access control
- `PremiumRepository` - Premium status persistence

**Dependencies:** Google Play Billing, Apple StoreKit, platform-native secure storage

---

## External APIs

### Google Play Billing API

- **Purpose:** Premium feature purchase processing and verification
- **Authentication:** App signing and Google Play Console configuration
- **Key Endpoints:** `BillingClient.queryPurchases()`, `BillingClient.launchBillingFlow()`
- **Integration Notes:** Offline verification support, 15% platform fee for revenue under $1M annually

### Google Drive API v3

- **Purpose:** Premium backup and restore functionality for user data
- **Base URL:** <https://www.googleapis.com/drive/v3>
- **Authentication:** OAuth 2.0 with limited scope permissions
- **Key Endpoints:** `POST /files`, `GET /files/{fileId}`, `GET /files`
- **Integration Notes:** Requires user consent, graceful offline degradation

---

## Core Workflows

### System Initialization Workflow

```mermaid
sequenceDiagram
    participant User
    participant UI as User Interface
    participant DB as SQLite Database
    participant Premium as Premium Service
    
    User->>UI: Launch App
    UI->>DB: Initialize Database
    DB->>UI: Database Ready
    UI->>Premium: Check Premium Status
    Premium->>UI: Premium Status Response
    UI->>User: Display Main Dashboard
```

### Premium Purchase Workflow

```mermaid
sequenceDiagram
    participant User
    participant UI as Premium UI
    participant Premium as Premium Service
    participant Billing as Google Play Billing
    participant Security as Platform Security
    
    User->>UI: Select Premium Feature
    UI->>Premium: Check Premium Status
    Premium->>UI: Not Premium User
    UI->>User: Display Premium Upgrade
    User->>UI: Initiate Purchase
    UI->>Billing: Launch Billing Flow
    Billing->>User: Payment Processing
    User->>Billing: Complete Payment
    Billing->>Premium: Purchase Token
    Premium->>Security: Store Purchase Token
    Premium->>UI: Premium Activated
    UI->>User: Premium Features Unlocked
```

---

## Database Schema

### Mobile-Optimized SQLite Schema

```sql
-- Enable mobile performance optimizations
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA foreign_keys = ON;

-- Core Tables
CREATE TABLE recipes (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_time INTEGER,
    is_premium BOOLEAN DEFAULT FALSE,
    success_rate REAL DEFAULT 0.0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cultures (
    id TEXT PRIMARY KEY,
    recipe_id TEXT NOT NULL,
    batch_name TEXT NOT NULL,
    start_date DATETIME NOT NULL,
    outcome TEXT CHECK (outcome IN ('success', 'failure', 'partial_success', 'in_progress')),
    temperature REAL,
    humidity REAL,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE
);

CREATE TABLE culture_photos (
    id TEXT PRIMARY KEY,
    culture_id TEXT NOT NULL,
    file_path TEXT NOT NULL,
    capture_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    stage TEXT CHECK (stage IN ('initial', 'progress', 'final')),
    compression_quality INTEGER DEFAULT 85,
    notes TEXT,
    FOREIGN KEY (culture_id) REFERENCES cultures(id) ON DELETE CASCADE
);

CREATE TABLE premium_status (
    user_device_id TEXT PRIMARY KEY,
    is_premium BOOLEAN DEFAULT FALSE,
    purchase_token TEXT,
    purchase_date DATETIME,
    purchase_platform TEXT CHECK (purchase_platform IN ('google_play', 'apple_app_store')),
    last_verified DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Performance Indexes
CREATE INDEX idx_recipes_created_at ON recipes(created_at DESC);
CREATE INDEX idx_cultures_recipe_id ON cultures(recipe_id);
CREATE INDEX idx_culture_photos_culture_id ON culture_photos(culture_id);

-- Data Integrity Triggers
CREATE TRIGGER update_recipe_success_rate
AFTER INSERT ON cultures
BEGIN
    UPDATE recipes 
    SET success_rate = (
        SELECT ROUND(
            (COUNT(CASE WHEN outcome = 'success' THEN 1 END) * 100.0) / COUNT(*), 2
        )
        FROM cultures 
        WHERE recipe_id = NEW.recipe_id 
        AND outcome IN ('success', 'failure', 'partial_success')
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.recipe_id;
END;
```

---

## Frontend Architecture

### Clean Architecture Organization

```
lib/
├── core/                           # Core utilities and constants
│   ├── constants/
│   ├── errors/
│   ├── network/
│   └── utils/
├── data/                           # Data layer implementation
│   ├── datasources/
│   ├── models/
│   └── repositories/
├── domain/                         # Business logic layer
│   ├── entities/
│   ├── repositories/
│   └── usecases/
├── presentation/                   # UI layer
│   ├── screens/
│   ├── widgets/
│   └── providers/
└── shared/                         # Shared utilities
    ├── themes/
    ├── routing/
    └── services/
```

### State Management with Riverpod

```dart
// Core providers for app-wide state
final databaseProvider = Provider<DatabaseHelper>((ref) => DatabaseHelper());
final premiumServiceProvider = Provider<PremiumService>((ref) => PremiumService());

// Repository providers
final recipeRepositoryProvider = Provider<RecipeRepository>((ref) {
  return RecipeRepositoryImpl(ref.watch(databaseProvider));
});

// State providers for UI
final recipesProvider = StateNotifierProvider<RecipeNotifier, RecipeState>((ref) {
  return RecipeNotifier(ref.watch(recipeRepositoryProvider));
});
```

---

## Security and Performance

### Security Architecture

**Privacy-by-Design Principles:**

- **No User Authentication Required:** Direct app access without login, registration, or user accounts
- **Local Data Storage Only:** All personal data remains on user's device with no cloud transmission in free tier
- **Minimal Data Collection:** No analytics, tracking, or personal information collection
- **Transparent Data Handling:** Clear disclosure of what data stays local vs. premium cloud sync

**Data Protection:**

- **Local Data Encryption:** SQLite database encryption using platform-native libraries (SQLCipher)
- **App Sandbox Security:** Leverages iOS/Android app sandboxing for data isolation
- **Premium Token Storage:** Secure storage of Google Play Billing receipts using iOS Keychain or Android EncryptedSharedPreferences

### Performance Optimization

**Mobile Performance Targets:**

- **Launch Time:** ≤ 3 seconds cold start on target devices (iOS 12+, Android 8.0+)
- **Database Operations:** ≤ 100ms for standard CRUD operations
- **Photo Capture:** ≤ 2 seconds from tap to processed image storage
- **Offline Performance:** Full functionality without network dependency

**Technical Optimizations:**

- **Database Indexing:** Strategic indexes on frequently queried fields
- **Image Optimization:** Automatic compression and resizing for mobile storage efficiency
- **Memory Management:** Efficient widget disposal and image caching strategies
- **Battery Optimization:** Background task limitation and efficient polling strategies

---

## Testing Strategy

### Testing Pyramid

```
        E2E Tests (Widget Integration)
       /                            \
    Integration Tests (Repository & Services)
   /                                        \
Flutter Widget Tests              Dart Unit Tests
```

### Test Organization

**Flutter Widget Tests:**

```
test/widget_tests/
├── screens/
│   ├── recipe_management_screen_test.dart
│   └── culture_documentation_screen_test.dart
├── components/
│   ├── photo_capture_widget_test.dart
│   └── premium_gate_widget_test.dart
└── workflows/
    └── recipe_creation_flow_test.dart
```

**Integration Tests:**

```
integration_test/
├── offline_functionality_test.dart
├── premium_feature_integration_test.dart
└── analytics_calculation_test.dart
```

---

## Coding Standards

### Critical Flutter Rules

- **Repository Pattern:** Always access data through repository interfaces, never directly call SQLite operations from UI components
- **Premium Feature Gating:** All premium features must check status through the centralized `PremiumService`
- **Offline-First Design:** Never assume network connectivity - all features must work offline
- **State Management:** Use Riverpod providers for all state management
- **Photo Management:** Always compress images immediately during capture
- **Error Handling:** All async operations must include proper error handling
- **Database Transactions:** Use SQLite transactions for multi-step operations
- **Platform-Native Security:** Store sensitive data using platform-native security

### Naming Conventions

| Element | Convention | Example |
|---------|------------|----------|
| **Screens** | PascalCase + Screen suffix | `RecipeManagementScreen` |
| **Widgets** | PascalCase + Widget suffix | `PhotoCaptureWidget` |
| **Models** | PascalCase | `Recipe`, `CultureLog` |
| **Repositories** | PascalCase + Repository suffix | `RecipeRepository` |
| **Services** | PascalCase + Service suffix | `PremiumService` |
| **Providers** | camelCase + Provider suffix | `recipeProvider` |
| **Database Tables** | snake_case | `recipes`, `culture_logs` |

---

## Error Handling Strategy

### Error Response Format

```dart
class AppError {
  final String code;
  final String message;
  final String userMessage;
  final ErrorSeverity severity;
  final DateTime timestamp;
  final bool canRetry;
  
  const AppError({
    required this.code,
    required this.message,
    required this.userMessage,
    required this.severity,
    required this.timestamp,
    this.canRetry = false,
  });
}

enum ErrorSeverity { info, warning, error, critical }
```

### Error Handling Implementation

```dart
class ErrorHandler {
  static AppError handleRepositoryError(Exception exception) {
    switch (exception.runtimeType) {
      case DatabaseException:
        return AppError(
          code: 'DB_ERROR',
          message: exception.toString(),
          userMessage: 'Unable to save data locally. Please try again.',
          severity: ErrorSeverity.error,
          timestamp: DateTime.now(),
          canRetry: true,
        );
      
      case NetworkException:
        return AppError(
          code: 'NETWORK_ERROR',
          message: exception.toString(),
          userMessage: 'Working offline. Some features may be limited.',
          severity: ErrorSeverity.warning,
          timestamp: DateTime.now(),
          canRetry: true,
        );
      
      default:
        return AppError(
          code: 'UNKNOWN_ERROR',
          message: exception.toString(),
          userMessage: 'Something went wrong. Please try again.',
          severity: ErrorSeverity.error,
          timestamp: DateTime.now(),
          canRetry: true,
        );
    }
  }
}
```

---

## Monitoring and Observability

### Local Monitoring Stack

**Application Performance Metrics:**

- **Launch Time:** App startup duration (target: ≤ 3 seconds)
- **Database Query Performance:** SQLite operation timing (target: ≤ 100ms)
- **Photo Processing Time:** Image compression and storage duration (target: ≤ 2 seconds)
- **Memory Usage:** RAM consumption tracking (critical for 2GB+ devices)
- **Storage Usage:** Local database size and photo storage tracking

**System Health Metrics:**

- **Component Availability:** Health checks for each major component
- **External API Response Times:** Google Drive and Play Billing performance
- **Circuit Breaker Status:** External API failure tracking
- **Backup Queue Status:** Offline backup queue health (premium feature)

### Local Monitoring Implementation

```dart
class LocalMonitoringService {
  static Future<T> trackPerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();
      _recordPerformanceMetric(operationName, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      _logger.error('Operation failed: $operationName', e);
      rethrow;
    }
  }
  
  static Future<StorageHealth> checkStorageHealth() async {
    final dbSize = await _getDatabaseSize();
    final photoSize = await _getPhotoStorageSize();
    final totalStorage = await _getAvailableStorage();
    
    return StorageHealth(
      databaseSize: dbSize,
      photoStorageSize: photoSize,
      availableStorage: totalStorage,
      recommendCleanup: photoSize > totalStorage * 0.7,
    );
  }
}
```

---

## Development Workflow

### Prerequisites

```bash
# Install Flutter SDK 3.16.0+
flutter --version

# Install platform tools
# Android Studio (for Android development)
# Xcode (for iOS development)

# Install coverage tools
dart pub global activate coverage
```

### Development Commands

```bash
# Start development
flutter run

# Run tests
flutter test
flutter test integration_test/

# Generate coverage
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html

# Build for release
flutter build apk --release
flutter build ios --release
```

### Environment Configuration

```bash
# Development (.env)
DATABASE_NAME=culturestack_dev.db
GOOGLE_PLAY_BILLING_TEST_MODE=true
GOOGLE_DRIVE_API_KEY=your_dev_api_key
ENABLE_ANALYTICS_LOGGING=true
MAX_PHOTO_SIZE_MB=10
COMPRESSION_QUALITY=85

# Production (.env.production)
DATABASE_NAME=culturestack.db
GOOGLE_PLAY_BILLING_TEST_MODE=false
GOOGLE_DRIVE_API_KEY=your_prod_api_key
ENABLE_ANALYTICS_LOGGING=false
MAX_PHOTO_SIZE_MB=5
COMPRESSION_QUALITY=80
```

---

## Deployment Architecture

### Deployment Strategy

**Mobile App Store Deployment:**

- **Platform:** Google Play Store, Apple App Store
- **Build Command:** `flutter build apk --release`, `flutter build ios --release`
- **Distribution:** Consumer app stores with enterprise distribution for educational institutions
- **CI/CD:** GitHub Actions for automated testing and build generation

### CI/CD Pipeline

```yaml
name: Flutter CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - run: flutter pub get
      - run: flutter test
      - run: flutter test integration_test/
      
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter build apk --release
      - uses: actions/upload-artifact@v3
        with:
          name: release-apk
          path: build/app/outputs/flutter-apk/app-release.apk
```

---

## Architecture Summary

This comprehensive fullstack architecture document establishes CultureStack as a robust, offline-first mobile application for tissue culture management. Key architectural principles include:

**✅ Core Principles Achieved:**

- **Offline-First:** Complete functionality without internet dependency
- **No-Login Architecture:** Direct app access preserving user privacy
- **Mobile-Optimized:** Performance targets for older devices (Android 8.0+, iOS 12+)
- **Premium Integration:** Centralized feature gating through Premium Service
- **Component Consolidation:** 7 robust components reducing complexity
- **Privacy-by-Design:** Local data storage with optional premium cloud sync

**Technical Excellence:**

- Flutter 3.16+ with Clean Architecture organization
- SQLite 3.40+ with mobile performance optimization
- Riverpod 2.4+ for reactive state management
- Google Play Billing integration with 15% fee structure
- Comprehensive testing strategy with widget, unit, and integration tests
- Local monitoring and performance tracking
- Robust error handling with graceful degradation

This architecture provides a solid foundation for developing a professional-grade mobile application that serves hobbyists, educational institutions, and small commercial operations in the tissue culture domain.

---

*Document Version: 1.0*  
*Last Updated: 2025-01-27*  
*Architecture Type: Fullstack Mobile (Offline-First)*
