import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

void setupPathProvider() {
  const MethodChannel channel =
      MethodChannel('plugins.flutter.io/path_provider');

  channel.setMockMethodCallHandler((MethodCall methodCall) async {
    if (methodCall.method == 'getApplicationDocumentsDirectory') {
      final Directory directory = await Directory.systemTemp.createTemp();
      return directory.path;
    }
    return null;
  });
}
