# Tech Stack

### Core Technologies

| Component | Technology | Version | Purpose | Why Chosen |
|-----------|------------|---------|---------|------------|
| **Mobile Framework** | Flutter | 3.16+ | Cross-platform mobile development | Mature ecosystem, offline-first capabilities, single codebase for iOS/Android |
| **Programming Language** | Dart | 3.2+ | Application development language | Native Flutter language, strong typing, excellent performance |
| **Local Database** | SQLite | 3.40+ | Offline-first data storage | Embedded database, reliable offline operation, excellent mobile performance |
| **State Management** | Riverpod | 2.4+ | Reactive state management | Compile-time safety, excellent testing support, provider pattern |
| **Payment Processing** | Google Play Billing | Latest | In-app purchase management | Platform compliance, 15% fee structure, offline verification support |
| **Cloud Storage (Premium)** | Google Drive API | v3 | Premium backup functionality | User-familiar platform, generous free storage, robust API |
| **Local Security** | Platform Native | - | Secure data storage | iOS Keychain, Android EncryptedSharedPreferences |
| **Image Processing** | Flutter Image | Latest | Photo compression and management | Built-in optimization, memory-efficient processing |
| **Testing Framework** | Flutter Test | Latest | Widget and unit testing | Comprehensive testing ecosystem, built-in mocking |
| **CI/CD** | GitHub Actions | Latest | Automated testing and deployment | Free for open source, excellent Flutter support |

### Architecture Patterns

- **Offline-First Mobile Architecture:** Complete functionality without internet connectivity
- **Clean Architecture:** Organized into core, data, domain, presentation, and shared layers
- **Repository Pattern:** Abstract data access layer with premium-aware methods
- **Provider Pattern:** Riverpod for dependency injection and state management
- **Circuit Breaker Pattern:** Graceful degradation for external API failures
- **Privacy-by-Design:** No-login architecture with local-only data storage

---

