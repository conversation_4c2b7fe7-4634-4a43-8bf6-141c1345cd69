import '../../domain/entities/culture.dart';

/// Culture model for data layer with SQLite serialization
///
/// [Source: architecture/database-schema.md]
class CultureModel extends Culture {
  const CultureModel({
    required super.id,
    required super.recipeId,
    required super.batchName,
    required super.startDate,
    required super.outcome,
    super.temperature,
    super.humidity,
    super.notes,
    required super.createdAt,
  });

  /// Create CultureModel from Map (SQLite row)
  factory CultureModel.fromMap(Map<String, dynamic> map) {
    return CultureModel(
      id: map['id'] as String,
      recipeId: map['recipe_id'] as String,
      batchName: map['batch_name'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      outcome: map['outcome'] as String,
      temperature: map['temperature'] as double?,
      humidity: map['humidity'] as double?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  /// Convert CultureModel to Map for SQLite storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'recipe_id': recipeId,
      'batch_name': batchName,
      'start_date': startDate.toIso8601String(),
      'outcome': outcome,
      'temperature': temperature,
      'humidity': humidity,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Create CultureModel from Culture entity
  factory CultureModel.fromEntity(Culture culture) {
    return CultureModel(
      id: culture.id,
      recipeId: culture.recipeId,
      batchName: culture.batchName,
      startDate: culture.startDate,
      outcome: culture.outcome,
      temperature: culture.temperature,
      humidity: culture.humidity,
      notes: culture.notes,
      createdAt: culture.createdAt,
    );
  }

  /// Convert to Culture entity
  Culture toEntity() {
    return Culture(
      id: id,
      recipeId: recipeId,
      batchName: batchName,
      startDate: startDate,
      outcome: outcome,
      temperature: temperature,
      humidity: humidity,
      notes: notes,
      createdAt: createdAt,
    );
  }

  @override
  CultureModel copyWith({
    String? id,
    String? recipeId,
    String? batchName,
    DateTime? startDate,
    String? outcome,
    double? temperature,
    double? humidity,
    String? notes,
    DateTime? createdAt,
  }) {
    return CultureModel(
      id: id ?? this.id,
      recipeId: recipeId ?? this.recipeId,
      batchName: batchName ?? this.batchName,
      startDate: startDate ?? this.startDate,
      outcome: outcome ?? this.outcome,
      temperature: temperature ?? this.temperature,
      humidity: humidity ?? this.humidity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
