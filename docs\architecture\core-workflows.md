# Core Workflows

### System Initialization Workflow

```mermaid
sequenceDiagram
    participant User
    participant UI as User Interface
    participant DB as SQLite Database
    participant Premium as Premium Service
    
    User->>UI: Launch App
    UI->>DB: Initialize Database
    DB->>UI: Database Ready
    UI->>Premium: Check Premium Status
    Premium->>UI: Premium Status Response
    UI->>User: Display Main Dashboard
```

### Premium Purchase Workflow

```mermaid
sequenceDiagram
    participant User
    participant UI as Premium UI
    participant Premium as Premium Service
    participant Billing as Google Play Billing
    participant Security as Platform Security
    
    User->>UI: Select Premium Feature
    UI->>Premium: Check Premium Status
    Premium->>UI: Not Premium User
    UI->>User: Display Premium Upgrade
    User->>UI: Initiate Purchase
    UI->>Billing: Launch Billing Flow
    Billing->>User: Payment Processing
    User->>Billing: Complete Payment
    Billing->>Premium: Purchase Token
    Premium->>Security: Store Purchase Token
    Premium->>UI: Premium Activated
    UI->>User: Premium Features Unlocked
```

---

