import '../../domain/entities/premium.dart';

/// Premium model for data layer with SQLite serialization
///
/// [Source: architecture/database-schema.md]
class PremiumModel extends Premium {
  const PremiumModel({
    required super.userDeviceId,
    required super.isPremium,
    super.purchaseToken,
    super.purchaseDate,
    super.purchasePlatform,
    required super.lastVerified,
  });

  /// Create PremiumModel from Map (SQLite row)
  factory PremiumModel.fromMap(Map<String, dynamic> map) {
    return PremiumModel(
      userDeviceId: map['user_device_id'] as String,
      isPremium: (map['is_premium'] as int) == 1,
      purchaseToken: map['purchase_token'] as String?,
      purchaseDate: map['purchase_date'] != null
          ? DateTime.parse(map['purchase_date'] as String)
          : null,
      purchasePlatform: map['purchase_platform'] as String?,
      lastVerified: DateTime.parse(map['last_verified'] as String),
    );
  }

  /// Convert PremiumModel to Map for SQLite storage
  Map<String, dynamic> toMap() {
    return {
      'user_device_id': userDeviceId,
      'is_premium': isPremium ? 1 : 0,
      'purchase_token': purchaseToken,
      'purchase_date': purchaseDate?.toIso8601String(),
      'purchase_platform': purchasePlatform,
      'last_verified': lastVerified.toIso8601String(),
    };
  }

  /// Create PremiumModel from Premium entity
  factory PremiumModel.fromEntity(Premium premium) {
    return PremiumModel(
      userDeviceId: premium.userDeviceId,
      isPremium: premium.isPremium,
      purchaseToken: premium.purchaseToken,
      purchaseDate: premium.purchaseDate,
      purchasePlatform: premium.purchasePlatform,
      lastVerified: premium.lastVerified,
    );
  }

  /// Convert to Premium entity
  Premium toEntity() {
    return Premium(
      userDeviceId: userDeviceId,
      isPremium: isPremium,
      purchaseToken: purchaseToken,
      purchaseDate: purchaseDate,
      purchasePlatform: purchasePlatform,
      lastVerified: lastVerified,
    );
  }

  @override
  PremiumModel copyWith({
    String? userDeviceId,
    bool? isPremium,
    String? purchaseToken,
    DateTime? purchaseDate,
    String? purchasePlatform,
    DateTime? lastVerified,
  }) {
    return PremiumModel(
      userDeviceId: userDeviceId ?? this.userDeviceId,
      isPremium: isPremium ?? this.isPremium,
      purchaseToken: purchaseToken ?? this.purchaseToken,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      purchasePlatform: purchasePlatform ?? this.purchasePlatform,
      lastVerified: lastVerified ?? this.lastVerified,
    );
  }
}
